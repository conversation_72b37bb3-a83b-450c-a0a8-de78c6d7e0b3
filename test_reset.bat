@echo off
echo ========================================
echo AFOV Password Reset Test Script
echo ========================================

echo.
echo 1. Testing with internal email (@afop.vn)...
echo This should use Firebase Auth (fast)
echo.
flutter run --dart-define=RESET_URL=https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset

echo.
echo ========================================
echo TEST INSTRUCTIONS:
echo ========================================
echo.
echo 1. Open the app
echo 2. Go to "Quên mật khẩu?" screen
echo 3. Test with these emails:
echo.
echo    INTERNAL EMAIL (Firebase):
echo    - <EMAIL>
echo    - Should show: "qua Firebase"
echo    - Should receive email quickly
echo.
echo    EXTERNAL EMAIL (SMTP):
echo    - <EMAIL>
echo    - <EMAIL>  
echo    - Should show: "qua SMTP nội bộ"
echo    - Should receive email via company SMTP
echo.
echo 4. Check email inbox (and spam folder)
echo 5. Click reset link to verify it works
echo.
echo ========================================
echo TROUBLESHOOTING:
echo ========================================
echo.
echo If emails not received:
echo 1. Check Firebase Functions logs:
echo    firebase functions:log --only sendPasswordReset
echo.
echo 2. Test Cloud Function directly:
echo    curl -X POST https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset ^
echo         -H "Content-Type: application/json" ^
echo         -d "{\"email\":\"<EMAIL>\"}"
echo.
echo 3. Check SMTP credentials in Firebase config:
echo    firebase functions:config:get
echo.
pause

# Hướng dẫn cài đặt SMTP Reset Password

## 1. Cài đặt Firebase CLI (nếu chưa có)
```bash
npm install -g firebase-tools
firebase login
```

## 2. Khởi tạo project (nếu chưa có)
```bash
firebase init functions
# Chọn project: khuyetcong-a4a7a
# Chọn language: JavaScript
# Chọn ESLint: No (để đơn giản)
# Install dependencies: Yes
```

## 3. <PERSON><PERSON><PERSON> hình SMTP credentials
```bash
# Cấu hình email và password SMTP
firebase functions:config:set smtp.user="<EMAIL>"
firebase functions:config:set smtp.password="Longan1234567,"

# Xem cấu hình hiện tại
firebase functions:config:get
```

## 4. Deploy Cloud Functions
```bash
cd functions
npm install
cd ..
firebase deploy --only functions
```

## 5. Lấy URL của Cloud Function
Sau khi deploy thành công, bạn sẽ nhận được URL như:
```
https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
```

## 6. Cập nhật Flutter app
Chạy app với custom endpoint:
```bash
flutter run --dart-define=RESET_URL=https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
```

Hoặc build release:
```bash
flutter build apk --dart-define=RESET_URL=https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
```

## 7. Test thử
1. Mở app
2. Vào màn hình Reset Password
3. Nhập email @gmail.com hoặc @yahoo.com
4. Kiểm tra email (có thể trong spam folder)

## 8. Troubleshooting

### Nếu email không gửi được:
1. Kiểm tra logs:
```bash
firebase functions:log --only sendPasswordReset
```

2. Test local:
```bash
firebase emulators:start --only functions
# URL local: http://localhost:5001/khuyetcong-a4a7a/asia-southeast1/sendPasswordReset
```

3. Kiểm tra SMTP credentials:
- Đảm bảo tài khoản <EMAIL> có quyền gửi email
- Nếu bật MFA, cần dùng App Password thay vì password thường

### Nếu muốn thay đổi SMTP settings:
```bash
firebase functions:config:set smtp.user="<EMAIL>"
firebase functions:config:set smtp.password="password-moi"
firebase deploy --only functions
```

## 9. Monitoring
- Firebase Console → Functions → Logs
- Firebase Console → Functions → Metrics
- Health check: https://your-function-url/healthCheck

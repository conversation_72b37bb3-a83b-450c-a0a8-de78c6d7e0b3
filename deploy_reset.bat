@echo off
echo ========================================
echo AFOV Password Reset Deployment Script
echo ========================================

echo.
echo 1. Checking Firebase CLI...
firebase --version
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI not found. Please install:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

echo.
echo 2. Installing Cloud Functions dependencies...
cd functions
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo 3. Setting up SMTP configuration...
echo Setting SMTP user: <EMAIL>
firebase functions:config:set smtp.user="<EMAIL>"

echo Setting SMTP password...
firebase functions:config:set smtp.password="Longan1234567,"

echo.
echo 4. Current configuration:
firebase functions:config:get

echo.
echo 5. Deploying Cloud Functions...
firebase deploy --only functions
if %errorlevel% neq 0 (
    echo ERROR: Failed to deploy functions
    pause
    exit /b 1
)

echo.
echo ========================================
echo DEPLOYMENT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Your Cloud Function URL should be:
echo https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
echo.
echo To test the app with SMTP:
echo flutter run --dart-define=RESET_URL=https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
echo.
echo To build release APK:
echo flutter build apk --dart-define=RESET_URL=https://asia-southeast1-khuyetcong-a4a7a.cloudfunctions.net/sendPasswordReset
echo.
pause

const functions = require('firebase-functions/v2/https');
const admin = require('firebase-admin');
const nodemailer = require('nodemailer');

// Khởi tạo Firebase Admin
admin.initializeApp();

// C<PERSON>u hình SMTP cho AFOV IT Dev
const transporter = nodemailer.createTransporter({
  host: 'smtp.office365.com',
  port: 587,
  secure: false, // TLS
  auth: {
    user: functions.config().smtp?.user || '<EMAIL>',
    pass: functions.config().smtp?.password || 'Longan1234567,'
  }
});

// Cloud Function để gửi email reset password
exports.sendPasswordReset = functions.onRequest(
  {
    cors: true,
    region: 'asia-southeast1', // Gần Việt Nam hơn
  },
  async (req, res) => {
    // Chỉ cho phép POST
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    try {
      const { email } = req.body;
      
      // Validate email
      if (!email || !email.includes('@')) {
        return res.status(400).json({ error: 'Email không hợp lệ' });
      }

      console.log(`🔄 Đang xử lý reset password cho: ${email}`);

      // Tạo link reset từ Firebase Admin
      const resetLink = await admin.auth().generatePasswordResetLink(email, {
        url: 'https://it.afop.vn/reset/', // URL tùy chỉnh nếu có
        handleCodeInApp: true,
      });

      // Template email đẹp hơn
      const emailTemplate = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2196F3; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { 
              display: inline-block; 
              background: #2196F3; 
              color: white; 
              padding: 12px 24px; 
              text-decoration: none; 
              border-radius: 5px; 
              margin: 20px 0;
            }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>🔐 Đặt lại mật khẩu</h2>
            </div>
            <div class="content">
              <p>Xin chào,</p>
              <p>Bạn vừa yêu cầu đặt lại mật khẩu cho tài khoản <strong>${email}</strong>.</p>
              <p>Nhấn vào nút bên dưới để đặt lại mật khẩu:</p>
              <p style="text-align: center;">
                <a href="${resetLink}" class="button">🔄 Đặt lại mật khẩu</a>
              </p>
              <p><strong>Lưu ý:</strong></p>
              <ul>
                <li>Link này sẽ hết hạn sau 1 giờ</li>
                <li>Nếu bạn không yêu cầu đặt lại mật khẩu, hãy bỏ qua email này</li>
                <li>Không chia sẻ link này với ai khác</li>
              </ul>
            </div>
            <div class="footer">
              <p>Email được gửi từ hệ thống AFOV IT Dev</p>
              <p>Nếu có vấn đề, liên hệ: <EMAIL></p>
            </div>
          </div>
        </body>
        </html>
      `;

      // Gửi email qua SMTP nội bộ
      await transporter.sendMail({
        from: '"AFOV IT Dev 🔧" <<EMAIL>>',
        to: email,
        subject: '🔐 Yêu cầu đặt lại mật khẩu - AFOV System',
        html: emailTemplate,
        // Fallback text version
        text: `
Xin chào,

Bạn vừa yêu cầu đặt lại mật khẩu cho tài khoản ${email}.

Nhấn vào link bên dưới để đặt lại:
${resetLink}

Link này sẽ hết hạn sau 1 giờ.
Nếu bạn không yêu cầu đặt lại mật khẩu, hãy bỏ qua email này.

---
AFOV IT Dev
<EMAIL>
        `
      });

      console.log(`✅ Email reset đã gửi thành công tới: ${email}`);
      
      res.status(200).json({ 
        success: true, 
        message: `Email đã được gửi tới ${email}`,
        method: 'SMTP nội bộ'
      });

    } catch (error) {
      console.error(`❌ Lỗi gửi email:`, error);
      
      // Trả về lỗi chi tiết hơn
      let errorMessage = 'Có lỗi xảy ra khi gửi email';
      
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'Không tìm thấy tài khoản với email này';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Email không hợp lệ';
      } else if (error.message?.includes('SMTP')) {
        errorMessage = 'Lỗi kết nối SMTP. Vui lòng thử lại sau';
      }
      
      res.status(500).json({ 
        error: errorMessage,
        details: error.message 
      });
    }
  }
);

// Health check endpoint
exports.healthCheck = functions.onRequest((req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'AFOV Password Reset Service'
  });
});

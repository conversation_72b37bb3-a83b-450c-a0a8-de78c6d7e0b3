{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "hosting": {"public": "reset/html", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/reset-password", "function": "sendPasswordReset"}, {"source": "/api/health", "function": "healthCheck"}]}, "emulators": {"functions": {"port": 5001}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true}}
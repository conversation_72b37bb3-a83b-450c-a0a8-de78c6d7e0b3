import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _email = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _busy = false;

  // Nếu bạn có Cloud Function / API tự gửi qua AFOV IT Dev, set bằng --dart-define
  static const _customEndpoint =
      String.fromEnvironment('RESET_URL', defaultValue: '');

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;
    final email = _email.text.trim();

    setState(() => _busy = true);
    try {
      // Kiểm tra nếu là email nội bộ (@afop.vn) thì dùng Firebase
      // Còn lại dùng SMTP nội bộ để tránh bị spam filter
      final isInternalEmail = email.toLowerCase().endsWith('@afop.vn');

      if (_customEndpoint.isNotEmpty && !isInternalEmail) {
        // Gọi API/Function SMTP nội bộ cho email bên ngoài
        final res = await http.post(
          Uri.parse(_customEndpoint),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'email': email}),
        );
        if (res.statusCode >= 300) {
          throw Exception('Reset qua API lỗi: ${res.body}');
        }
      } else {
        // Dùng Firebase mặc định cho email nội bộ hoặc khi không có custom endpoint
        await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      }

      if (mounted) {
        final method = (_customEndpoint.isNotEmpty && !isInternalEmail)
            ? 'qua SMTP nội bộ'
            : 'qua Firebase';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã gửi email đặt lại mật khẩu tới $email ($method)'),
            duration: const Duration(seconds: 4),
          ),
        );
        Navigator.of(context).maybePop();
      }
    } on FirebaseAuthException catch (e) {
      _toast('Không gửi được email: ${e.code}');
    } catch (e) {
      _toast('$e');
    } finally {
      if (mounted) setState(() => _busy = false);
    }
  }

  void _toast(String m) =>
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(m)));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reset Password')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text('Nhập email để nhận link đặt lại mật khẩu.'),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline,
                            size: 16, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Lưu ý về gửi email:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '• Email @afop.vn: Gửi qua Firebase (nhanh)\n'
                      '• Email khác (@gmail.com, @yahoo.com...): Gửi qua SMTP nội bộ (tránh spam)',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _email,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  hintText: '<EMAIL> hoặc <EMAIL>',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
                validator: (v) => (v != null && v.contains('@'))
                    ? null
                    : 'Email không hợp lệ',
              ),
              const SizedBox(height: 16),
              FilledButton(
                onPressed: _busy ? null : _submit,
                child: Text(_busy ? 'Đang gửi…' : 'Gửi link reset'),
              ),
              if (_customEndpoint.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle_outline,
                            size: 16, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'SMTP nội bộ đã được kích hoạt',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

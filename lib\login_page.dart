import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'screens/reset_password_screen.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _email = TextEditingController();
  final _pass = TextEditingController();
  final _form = GlobalKey<FormState>();
  bool _busy = false;

  Future<void> _login() async {
    if (!_form.currentState!.validate()) return;
    setState(() => _busy = true);
    try {
      await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: _email.text.trim(),
        password: _pass.text,
      );
    } on FirebaseAuthException catch (e) {
      _toast('Đăng nhập lỗi: ${e.code}');
    } finally {
      if (mounted) setState(() => _busy = false);
    }
  }

  Future<void> _resetPassword() async {
    final email = _email.text.trim();
    if (email.isEmpty || !email.contains('@')) {
      _toast('Nhập email hợp lệ để nhận link đặt lại mật khẩu');
      return;
    }
    setState(() => _busy = true);
    try {
      // Sử dụng logic tương tự ResetPasswordScreen
      final isInternalEmail = email.toLowerCase().endsWith('@afop.vn');
      const customEndpoint =
          String.fromEnvironment('RESET_URL', defaultValue: '');

      if (customEndpoint.isNotEmpty && !isInternalEmail) {
        // Gọi API SMTP nội bộ cho email bên ngoài
        final res = await http.post(
          Uri.parse(customEndpoint),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'email': email}),
        );
        if (res.statusCode >= 300) {
          throw Exception('Reset qua API lỗi: ${res.body}');
        }
        const method = 'qua SMTP nội bộ';
        _toast('Đã gửi email đặt lại mật khẩu tới $email ($method)');
      } else {
        // Dùng Firebase mặc định cho email nội bộ
        await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
        const method = 'qua Firebase';
        _toast('Đã gửi email đặt lại mật khẩu tới $email ($method)');
      }
    } on FirebaseAuthException catch (e) {
      // các code hay gặp: user-not-found, invalid-email
      _toast('Không gửi được email: ${e.code}');
    } catch (e) {
      _toast('Lỗi: $e');
    } finally {
      if (mounted) setState(() => _busy = false);
    }
  }

  void _toast(String m) =>
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(m)));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Đăng nhập')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _form,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              TextFormField(
                controller: _email,
                decoration: const InputDecoration(labelText: 'Email'),
                keyboardType: TextInputType.emailAddress,
                validator: (v) =>
                    v != null && v.contains('@') ? null : 'Email không hợp lệ',
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: _pass,
                obscureText: true,
                decoration: const InputDecoration(labelText: 'Mật khẩu'),
                validator: (v) =>
                    (v ?? '').length >= 6 ? null : 'Ít nhất 6 ký tự',
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _busy ? null : _login,
                child: Text(_busy ? 'Đang xử lý...' : 'Đăng nhập'),
              ),
              TextButton(
                onPressed: _busy
                    ? null
                    : () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                              builder: (_) => const ResetPasswordScreen()),
                        );
                      },
                child: const Text('Quên mật khẩu?'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
